{"sourceConfigs": {"las": {"center": false, "transform": [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0], "colorDepth": "auto", "fp64": true, "skip": 1, "minTileSize": 1000}, "laz": {"center": false, "transform": [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0], "colorDepth": "auto", "fp64": true, "skip": 1, "minTileSize": 1000}, "ifc": {"excludeTypes": [], "minTileSize": 1000}, "gltf": {"reuseGeometries": true, "includeTextures": true, "includeNormals": false, "excludeTypes": [], "minTileSize": 1000, "externalMetadata": true}, "glb": {"reuseGeometries": true, "includeTextures": true, "includeNormals": false, "excludeTypes": [], "minTileSize": 1000, "externalMetadata": true}, "json": {"center": false, "transform": [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0], "minTileSize": 1000}}}