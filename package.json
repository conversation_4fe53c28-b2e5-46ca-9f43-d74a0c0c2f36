{"name": "@xeokit/xeokit-convert", "version": "1.3.0", "description": "JavaScript utilities to create .XKT files", "main": "index.js", "types": "./types/index.d.ts", "bin": {"xeokit-convert": "convert2xkt.js"}, "directories": {}, "scripts": {"build": "npm run docs && npm run types", "docs": "rimraf ./docs/* && npx esdoc", "types": "tsc"}, "repository": {"type": "git", "url": "git+https://github.com/xeokit/xeokit-convert.git"}, "exports": {".": {"import": "./src/index.js", "require": "./src/index.js", "types": "./types/index.d.ts"}, "./convert2xkt.conf.js": "./convert2xkt.conf.js", "./convert2xkt.conf.json": "./convert2xkt.conf.json", "./convert2xkt.js": {"import": "./convert2xkt.js", "require": "./convert2xkt.js", "types": "./types/convert2xkt.d.ts"}, "./src/index.js": {"import": "./src/index.js", "require": "./src/index.js", "types": "./types/index.d.ts"}, "./src/convert2xkt.js": {"import": "./src/convert2xkt.js", "require": "./src/convert2xkt.js", "types": "./types/convert2xkt.d.ts"}}, "type": "module", "keywords": ["xeolabs", "xeokit", "bim", "opensource", "ifc", "webgl", "xkt", "gltf", "glb", "<PERSON><PERSON><PERSON>", "laz", "gis"], "author": "<PERSON>", "license": "LICENSE", "bugs": {"url": "https://github.com/xeokit/xeokit-convert/issues"}, "homepage": "https://github.com/xeokit/xeokit-convert#readme", "dependencies": {"@loaders.gl/core": "^4.3.3", "@loaders.gl/gltf": "^4.3.3", "@loaders.gl/images": "^4.3.3", "@loaders.gl/json": "^4.3.3", "@loaders.gl/las": "^4.3.3", "@loaders.gl/obj": "^4.3.3", "@loaders.gl/ply": "^4.3.3", "@loaders.gl/polyfills": "^4.3.3", "@loaders.gl/textures": "^4.3.3", "@xeokit/xeokit-convert": "^1.3.0", "commander": "^11.0.0", "pako": "^2.1.0", "web-ifc": "0.0.68"}, "devDependencies": {"esdoc": "^1.1.0", "esdoc-node": "^1.0.5", "esdoc-standard-plugin": "^1.0.0", "rimraf": "^3.0.2", "typescript": "^5.8.3"}, "files": ["/src", "/types", "./convert2xkt.js", "./convert2xkt.conf.js", "./convert2xkt.conf.json"]}